import { getDb } from "@database";
import { patient } from "@database/schema";
import type { GetAPContactType, GetCCPatientType, WebhookContext } from "@type";
import {
	checkAndAddToBuffer,
	generatePatient<PERSON><PERSON>er<PERSON>ey,
} from "@utils/bufferManager";
import { logSyncError } from "@utils/errorLogger";
import { eq } from "drizzle-orm";
import { apClient } from "../api/apClient";

/**
 * Processes patient creation events from CC (CliniCore)
 *
 * This function handles the creation of new patients in the CC system and synchronizes
 * them to the AP (AutoPatient) platform. It implements the same business logic as the
 * ProcessPatientCreate job from v3Integration but with improved error handling and
 * performance optimizations.
 *
 * **Business Logic Flow:**
 * 1. Check buffer to prevent duplicate processing
 * 2. Validate required patient data (email or phone must be present)
 * 3. Check if patient already exists in local database
 * 4. Create or update local patient record with CC data
 * 5. Create or update corresponding contact in AP platform
 * 6. Handle custom field synchronization
 * 7. Log any errors for monitoring and debugging
 *
 * **Buffer Management:**
 * Uses timestamp-based buffer to prevent duplicate processing of the same patient
 * creation event within the configured buffer time window (default: 60 seconds).
 *
 * **Error Handling:**
 * - Validates email/phone requirements before processing
 * - Logs sync errors to database for monitoring
 * - Returns detailed error messages for debugging
 * - Gracefully handles API failures and database errors
 *
 * @param payload - Complete patient data from CC webhook event
 * @param payload.id - Unique CC patient ID
 * @param payload.email - Patient email address (required if phone is empty)
 * @param payload.phoneMobile - Patient mobile phone (required if email is empty)
 * @param payload.firstName - Patient first name
 * @param payload.lastName - Patient last name
 * @param payload.updatedAt - Timestamp of last update in CC
 * @param context - Webhook processing context containing request metadata
 * @param context.requestId - Unique request identifier for tracking
 * @param context.processedAt - Timestamp when processing started
 * @param context.event - Original webhook event data
 *
 * @returns Promise resolving to processing result object
 * @returns result.success - Boolean indicating if processing was successful
 * @returns result.message - Descriptive message about the processing outcome
 * @returns result.patientId - AP contact ID if creation was successful
 *
 * @throws {Error} When critical errors occur that prevent processing
 *
 * @example
 * ```typescript
 * const webhookPayload = {
 *   id: 12345,
 *   firstName: "John",
 *   lastName: "Doe",
 *   email: "<EMAIL>",
 *   phoneMobile: "+**********",
 *   updatedAt: "2024-01-01T12:00:00Z"
 * };
 *
 * const context = {
 *   requestId: "req_123",
 *   processedAt: new Date(),
 *   event: originalWebhookEvent
 * };
 *
 * const result = await processPatientCreate(webhookPayload, context);
 *
 * if (result.success) {
 *   console.log(`Patient created successfully: ${result.patientId}`);
 * } else {
 *   console.error(`Patient creation failed: ${result.message}`);
 * }
 * ```
 *
 * @see {@link processPatientUpdate} for handling patient updates
 * @see {@link createOrUpdateAPContact} for AP contact creation logic
 * @see {@link generatePatientBufferKey} for buffer key generation
 *
 * @since 1.0.0
 * @version 1.0.0
 */
export async function processPatientCreate(
	payload: GetCCPatientType,
	_context: WebhookContext,
): Promise<{ success: boolean; message: string; patientId?: string }> {
	try {
		console.log(`Processing patient create for CC ID: ${payload.id}`);

		// Check buffer to prevent duplicate processing
		const bufferKey = generatePatientBufferKey(
			"ProcessPatientCreate",
			payload.id,
		);
		if (checkAndAddToBuffer(bufferKey)) {
			return {
				success: true,
				message: `Patient creation recently processed, skipping. CC ID: ${payload.id}`,
			};
		}

		const db = getDb();

		// Check if patient already exists in our database
		const existingPatient = await db
			.select()
			.from(patient)
			.where(eq(patient.ccId, payload.id))
			.limit(1);

		let dbPatient = existingPatient[0];

		if (dbPatient?.apId) {
			console.log(`Patient already exists in AP, ID: ${dbPatient.apId}`);
			return {
				success: true,
				message: `Patient already exists in AP, ID: ${dbPatient.apId}`,
				patientId: dbPatient.apId,
			};
		}

		// Validate required data
		if (!payload?.email && !payload?.phoneMobile) {
			const message = `Email and phone are empty, dropping create patient request. CC ID: ${payload?.id}`;
			console.log(message);
			return {
				success: false,
				message,
			};
		}

		// Create or update patient record in database
		if (!dbPatient) {
			const [newPatient] = await db
				.insert(patient)
				.values({
					ccId: payload.id,
					email: payload.email || null,
					phone: payload.phoneMobile || null,
					ccData: payload,
					ccUpdatedAt: new Date(payload.updatedAt),
				})
				.returning();
			dbPatient = newPatient;
		} else {
			// Update existing patient with new CC data
			const [updatedPatient] = await db
				.update(patient)
				.set({
					email: payload.email || null,
					phone: payload.phoneMobile || null,
					ccData: payload,
					ccUpdatedAt: new Date(payload.updatedAt),
					updatedAt: new Date(),
				})
				.where(eq(patient.id, dbPatient.id))
				.returning();
			dbPatient = updatedPatient;
		}

		// Create contact in AP
		console.log(`🔄 About to call createOrUpdateAPContact for patient ID: ${payload.id}`);
		const apContactResult = await createOrUpdateAPContact(dbPatient, payload);
		console.log(`🔄 createOrUpdateAPContact result:`, apContactResult);

		if (apContactResult.success && apContactResult.apContact) {
			// Update database with AP data
			await db
				.update(patient)
				.set({
					apId: apContactResult.apContact.id,
					apData: apContactResult.apContact,
					apUpdatedAt: new Date(
						apContactResult.apContact.dateUpdated || new Date().toISOString(),
					),
					updatedAt: new Date(),
				})
				.where(eq(patient.id, dbPatient.id));

			console.log(
				`Patient created successfully in AP. CC ID: ${payload.id}, AP ID: ${apContactResult.apContact.id}`,
			);

			return {
				success: true,
				message: `Patient created successfully in AP. CC ID: ${payload.id}, AP ID: ${apContactResult.apContact.id}`,
				patientId: apContactResult.apContact.id,
			};
		} else {
			await logSyncError(
				"PATIENT_CREATE_AP_FAILED",
				new Error(apContactResult.message),
				payload.id,
				"CC",
				"PatientProcessor",
			);

			return {
				success: false,
				message: `Failed to create patient in AP: ${apContactResult.message}`,
			};
		}
	} catch (error) {
		await logSyncError(
			"PATIENT_CREATE_ERROR",
			error,
			payload.id,
			"CC",
			"PatientProcessor",
		);

		throw error;
	}
}

/**
 * Processes patient update events from CC (CliniCore)
 *
 * This function handles updates to existing patients in the CC system and synchronizes
 * the changes to the AP (AutoPatient) platform. It implements the same business logic
 * as the ProcessPatientUpdate job from v3Integration with enhanced error handling.
 *
 * **Business Logic Flow:**
 * 1. Check buffer to prevent duplicate processing of the same update
 * 2. Validate required patient data (email or phone must be present)
 * 3. Find existing patient in local database or create if not found
 * 4. Update local patient record with latest CC data
 * 5. Update corresponding contact in AP platform
 * 6. Synchronize custom fields between platforms
 * 7. Handle any errors and log for monitoring
 *
 * **Update vs Create Logic:**
 * If the patient doesn't exist locally, this function will automatically
 * delegate to processPatientCreate() to handle the initial creation.
 *
 * **Data Validation:**
 * - Requires either email or phone to be present (not both empty)
 * - Validates CC patient ID exists
 * - Ensures data integrity before AP synchronization
 *
 * **Performance Considerations:**
 * - Uses buffer management to prevent duplicate processing
 * - Optimized database queries with proper indexing
 * - Efficient AP API calls with error retry logic
 *
 * @param payload - Updated patient data from CC webhook event
 * @param payload.id - Unique CC patient ID
 * @param payload.email - Updated patient email address
 * @param payload.phoneMobile - Updated patient mobile phone
 * @param payload.firstName - Updated patient first name
 * @param payload.lastName - Updated patient last name
 * @param payload.updatedAt - Timestamp of the update in CC
 * @param context - Webhook processing context
 * @param context.requestId - Unique request identifier for tracking
 * @param context.processedAt - Timestamp when processing started
 * @param context.event - Original webhook event data
 *
 * @returns Promise resolving to processing result object
 * @returns result.success - Boolean indicating if update was successful
 * @returns result.message - Descriptive message about the update outcome
 * @returns result.patientId - AP contact ID if update was successful
 *
 * @throws {Error} When critical errors occur that prevent processing
 *
 * @example
 * ```typescript
 * const updatePayload = {
 *   id: 12345,
 *   firstName: "John",
 *   lastName: "Smith", // Updated last name
 *   email: "<EMAIL>", // Updated email
 *   phoneMobile: "+**********",
 *   updatedAt: "2024-01-02T12:00:00Z"
 * };
 *
 * const result = await processPatientUpdate(updatePayload, context);
 *
 * if (result.success) {
 *   console.log(`Patient updated successfully: ${result.patientId}`);
 * } else {
 *   console.error(`Patient update failed: ${result.message}`);
 * }
 * ```
 *
 * @see {@link processPatientCreate} for handling new patient creation
 * @see {@link createOrUpdateAPContact} for AP contact update logic
 * @see {@link generatePatientBufferKey} for buffer key generation
 *
 * @since 1.0.0
 * @version 1.0.0
 */
export async function processPatientUpdate(
	payload: GetCCPatientType,
	_context: WebhookContext,
): Promise<{ success: boolean; message: string; patientId?: string }> {
	try {
		console.log(`Processing patient update for CC ID: ${payload.id}`);

		// Check buffer to prevent duplicate processing
		const bufferKey = generatePatientBufferKey(
			"ProcessPatientUpdate",
			payload.id,
		);
		if (checkAndAddToBuffer(bufferKey)) {
			return {
				success: true,
				message: `Patient update recently processed, skipping. CC ID: ${payload.id}`,
			};
		}

		// Validate required data
		if (!payload?.email && !payload?.phoneMobile) {
			const message = `Email and phone are empty, dropping update patient request. CC ID: ${payload?.id}`;
			console.log(message);
			return {
				success: false,
				message,
			};
		}

		const db = getDb();

		// Find or create patient in database
		let dbPatient = await db
			.select()
			.from(patient)
			.where(eq(patient.ccId, payload.id))
			.limit(1)
			.then((results: (typeof patient.$inferSelect)[]) => results[0]);

		if (!dbPatient) {
			// Patient doesn't exist, create it
			const [newPatient] = await db
				.insert(patient)
				.values({
					ccId: payload.id,
					email: payload.email || null,
					phone: payload.phoneMobile || null,
					ccData: payload,
					ccUpdatedAt: new Date(payload.updatedAt),
				})
				.returning();
			dbPatient = newPatient;
		} else {
			// Update existing patient
			const [updatedPatient] = await db
				.update(patient)
				.set({
					email: payload.email || null,
					phone: payload.phoneMobile || null,
					ccData: payload,
					ccUpdatedAt: new Date(payload.updatedAt),
					updatedAt: new Date(),
				})
				.where(eq(patient.id, dbPatient.id))
				.returning();
			dbPatient = updatedPatient;
		}

		// Create or update contact in AP
		const apContactResult = await createOrUpdateAPContact(dbPatient, payload);

		if (apContactResult.success && apContactResult.apContact) {
			// Update database with AP data
			await db
				.update(patient)
				.set({
					apId: apContactResult.apContact.id,
					apData: apContactResult.apContact,
					apUpdatedAt: new Date(
						apContactResult.apContact.dateUpdated || new Date().toISOString(),
					),
					updatedAt: new Date(),
				})
				.where(eq(patient.id, dbPatient.id));

			console.log(
				`Patient updated successfully in AP. CC ID: ${payload.id}, AP ID: ${apContactResult.apContact.id}`,
			);

			return {
				success: true,
				message: `Patient updated successfully in AP. CC ID: ${payload.id}, AP ID: ${apContactResult.apContact.id}`,
				patientId: apContactResult.apContact.id,
			};
		} else {
			await logSyncError(
				"PATIENT_UPDATE_AP_FAILED",
				new Error(apContactResult.message),
				payload.id,
				"CC",
				"PatientProcessor",
			);

			return {
				success: false,
				message: `Failed to update patient in AP: ${apContactResult.message}`,
			};
		}
	} catch (error) {
		await logSyncError(
			"PATIENT_UPDATE_ERROR",
			error,
			payload.id,
			"CC",
			"PatientProcessor",
		);

		throw error;
	}
}

/**
 * Creates or updates a contact in AP based on patient data
 *
 * @param dbPatient - Patient record from database
 * @param ccPatient - Patient data from CC
 * @returns Result of AP contact operation
 */
async function createOrUpdateAPContact(
	dbPatient: typeof patient.$inferSelect,
	ccPatient: GetCCPatientType,
): Promise<{
	success: boolean;
	message: string;
	apContact?: GetAPContactType;
}> {
	console.log(`🔄 createOrUpdateAPContact called - dbPatient.apId: ${dbPatient.apId}, ccPatient.id: ${ccPatient.id}`);
	try {
		const contactData = {
			email: ccPatient.email || null,
			phone: ccPatient.phoneMobile || null,
			firstName: ccPatient.firstName || null,
			lastName: ccPatient.lastName || null,
			dateOfBirth: ccPatient.dob || null,
			gender: ccPatient.gender || null,
			tags: ["cc_api"],
			source: "cc" as const,
		};

		let apContact: GetAPContactType;

		if (dbPatient.apId) {
			// Update existing contact
			console.log(`Updating existing AP contact: ${dbPatient.apId}`);
			apContact = await apClient.contact.update(dbPatient.apId, contactData);
		} else {
			// Create new contact using upsert to handle duplicates
			console.log(`Creating new AP contact for CC patient: ${ccPatient.id}`);
			apContact = await apClient.contact.upsert(contactData);
		}

		return {
			success: true,
			message: "Contact created/updated successfully in AP",
			apContact,
		};
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		console.error(`Failed to create/update AP contact:`, error);

		return {
			success: false,
			message: `Failed to create/update AP contact: ${errorMessage}`,
		};
	}
}
